#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SorcerioModules Simple Professional Windows Installer Builder
Uses the existing main.spec file and creates a professional installer
"""

import os
import sys
import shutil
import subprocess
import logging
import requests
import zipfile
from pathlib import Path
import tempfile

# Configuration
COMPANY_NAME = "Ozmorph"
APP_NAME = "Sorcerio"
APP_VERSION = "1.0.0"
INSTALLER_OUTPUT_DIR = Path("C:/Users/<USER>/Desktop/sorcsetup")
PROJECT_ROOT = Path(__file__).resolve().parent

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleInstallerBuilder:
    def __init__(self):
        self.temp_dir = Path(tempfile.mkdtemp(prefix="sorcerio_simple_"))
        logger.info(f"Build environment created at: {self.temp_dir}")

    def build_with_existing_spec(self):
        """Build using the existing main.spec file"""
        logger.info("Building application with existing main.spec...")
        
        # Check if main.spec exists
        spec_file = PROJECT_ROOT / "main.spec"
        if not spec_file.exists():
            raise FileNotFoundError("main.spec file not found")
        
        # Create dist directory in temp
        dist_dir = self.temp_dir / "dist"
        dist_dir.mkdir(exist_ok=True)
        
        # Run PyInstaller with existing spec
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "--distpath", str(dist_dir),
            str(spec_file)
        ]
        
        logger.info(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=PROJECT_ROOT)
        
        if result.returncode != 0:
            logger.error(f"PyInstaller failed: {result.stderr}")
            # Try a simpler approach
            return self.build_simple_executable()
        
        logger.info("PyInstaller build completed successfully")
        
        # Find the built application
        app_exe = dist_dir / "main.exe"
        if app_exe.exists():
            return app_exe
        
        # Look for any exe file
        for file in dist_dir.rglob("*.exe"):
            return file
        
        raise FileNotFoundError("Built executable not found")

    def build_simple_executable(self):
        """Build a simple executable without complex dependencies"""
        logger.info("Building simple executable...")
        
        dist_dir = self.temp_dir / "dist"
        dist_dir.mkdir(exist_ok=True)
        
        # Create a simple launcher script
        launcher_content = f'''#!/usr/bin/env python3
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

# Set environment variables
os.environ["QTWEBENGINE_CHROMIUM_FLAGS"] = "--disable-gpu --disable-software-rasterizer"

try:
    # Import and run main
    import main
    if hasattr(main, 'main'):
        main.main()
    else:
        # Run the main block
        exec(open(project_root / "main.py").read())
except Exception as e:
    print(f"Error starting {APP_NAME}: {{e}}")
    input("Press Enter to exit...")
'''
        
        launcher_path = dist_dir / f"{APP_NAME}_Launcher.py"
        with open(launcher_path, 'w') as f:
            f.write(launcher_content)
        
        # Copy all project files
        app_dir = dist_dir / "app"
        app_dir.mkdir(exist_ok=True)
        
        # Core files
        core_files = [
            "main.py", "ui.py", "download.py", "upload.py", 
            "stats.py", "utils.py", "tailscale_manager.py",
            "requirements.txt", "instagram.ico", "x.ico"
        ]
        
        for file_name in core_files:
            src = PROJECT_ROOT / file_name
            if src.exists():
                shutil.copy2(src, app_dir / file_name)
        
        # Copy directories
        directories = ["configuration", "videos", "portable_chromium", "live_feed_thumbnails"]
        for dir_name in directories:
            src_dir = PROJECT_ROOT / dir_name
            if src_dir.exists():
                dst_dir = app_dir / dir_name
                if dst_dir.exists():
                    shutil.rmtree(dst_dir)
                shutil.copytree(src_dir, dst_dir)
        
        # Create batch launcher
        batch_content = f'''@echo off
title {APP_NAME} - Social Media Automation Tool
cd /d "%~dp0\\app"

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8 or higher from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

REM Install requirements if needed
if not exist "requirements_installed.flag" (
    echo Installing requirements...
    python -m pip install -r requirements.txt
    echo. > requirements_installed.flag
)

REM Start the application
python main.py

REM If we get here, the application has closed
if errorlevel 1 (
    echo.
    echo An error occurred. Press any key to close this window.
    pause >nul
)
'''
        
        batch_path = dist_dir / f"{APP_NAME}.bat"
        with open(batch_path, 'w') as f:
            f.write(batch_content)
        
        return batch_path

    def create_portable_installer(self, app_path):
        """Create a portable installer using 7-Zip self-extracting archive"""
        logger.info("Creating portable installer...")
        
        # Create installer directory
        installer_dir = self.temp_dir / "installer"
        installer_dir.mkdir(exist_ok=True)
        
        # Copy application files
        if app_path.is_file():
            # Single file
            app_files_dir = installer_dir / "SorcerioApp"
            app_files_dir.mkdir(exist_ok=True)
            shutil.copy2(app_path, app_files_dir)
            
            # Copy the entire dist directory
            dist_dir = app_path.parent
            for item in dist_dir.iterdir():
                if item.is_file():
                    shutil.copy2(item, app_files_dir)
                elif item.is_dir():
                    shutil.copytree(item, app_files_dir / item.name)
        else:
            # Directory
            app_files_dir = installer_dir / "SorcerioApp"
            shutil.copytree(app_path, app_files_dir)
        
        # Create installer script
        installer_script = f'''@echo off
title {APP_NAME} Professional Installer
echo ================================================================
echo {APP_NAME} - Professional Social Media Automation Tool
echo Company: {COMPANY_NAME}
echo Version: {APP_VERSION}
echo ================================================================
echo.

set INSTALL_DIR=%PROGRAMFILES%\\{COMPANY_NAME}\\{APP_NAME}

echo Installing {APP_NAME} to: %INSTALL_DIR%
echo.

REM Create installation directory
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files
echo Copying application files...
xcopy /E /I /Y "SorcerioApp\\*" "%INSTALL_DIR%\\"

REM Create desktop shortcut
echo Creating desktop shortcut...
set SHORTCUT_PATH=%USERPROFILE%\\Desktop\\{APP_NAME}.lnk
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT_PATH%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\{APP_NAME}.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\\app\\instagram.ico'; $Shortcut.Save()"

REM Create start menu shortcut
echo Creating start menu shortcut...
set STARTMENU_DIR=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\{COMPANY_NAME}
if not exist "%STARTMENU_DIR%" mkdir "%STARTMENU_DIR%"
set STARTMENU_SHORTCUT=%STARTMENU_DIR%\\{APP_NAME}.lnk
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\{APP_NAME}.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\\app\\instagram.ico'; $Shortcut.Save()"

echo.
echo ================================================================
echo Installation completed successfully!
echo ================================================================
echo.
echo {APP_NAME} has been installed to: %INSTALL_DIR%
echo Desktop shortcut created: %USERPROFILE%\\Desktop\\{APP_NAME}.lnk
echo Start menu shortcut created in: {COMPANY_NAME} folder
echo.
echo You can now launch {APP_NAME} from:
echo - Desktop shortcut
echo - Start Menu ^> {COMPANY_NAME} ^> {APP_NAME}
echo - Or run: "%INSTALL_DIR%\\{APP_NAME}.bat"
echo.
pause
'''
        
        installer_script_path = installer_dir / "install.bat"
        with open(installer_script_path, 'w') as f:
            f.write(installer_script)
        
        # Create readme
        readme_content = f'''{APP_NAME} Professional Installer
================================

Welcome to {APP_NAME} v{APP_VERSION}!

INSTALLATION INSTRUCTIONS:
1. Run "install.bat" as Administrator
2. Follow the on-screen instructions
3. Launch {APP_NAME} from desktop or start menu

SYSTEM REQUIREMENTS:
- Windows 10/11 (64-bit)
- Python 3.8 or higher
- 4GB RAM minimum
- 2GB free disk space
- Internet connection

FEATURES:
- Social media automation
- Content downloading and scheduling
- Multi-platform support (Instagram, Twitter, YouTube)
- Professional statistics and analytics

SUPPORT:
For support and documentation, visit: https://ozmorph.com

Copyright (c) 2024 {COMPANY_NAME}. All rights reserved.
'''
        
        readme_path = installer_dir / "README.txt"
        with open(readme_path, 'w') as f:
            f.write(readme_content)
        
        return installer_dir

    def create_zip_installer(self, installer_dir):
        """Create a ZIP-based installer"""
        logger.info("Creating ZIP installer...")
        
        # Ensure output directory exists
        INSTALLER_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
        
        # Create ZIP file
        zip_path = INSTALLER_OUTPUT_DIR / f"{APP_NAME}_Professional_Installer_v{APP_VERSION}.zip"
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(installer_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_path = file_path.relative_to(installer_dir)
                    zipf.write(file_path, arc_path)
        
        logger.info(f"ZIP installer created: {zip_path}")
        return zip_path

    def cleanup(self):
        """Clean up temporary files"""
        logger.info("Cleaning up temporary files...")
        try:
            shutil.rmtree(self.temp_dir)
            logger.info("Cleanup completed")
        except Exception as e:
            logger.warning(f"Cleanup failed: {e}")

    def build(self):
        """Main build process"""
        try:
            logger.info("Starting simple installer build process...")
            
            # Step 1: Build application
            app_path = self.build_with_existing_spec()
            
            # Step 2: Create portable installer
            installer_dir = self.create_portable_installer(app_path)
            
            # Step 3: Create ZIP installer
            final_installer = self.create_zip_installer(installer_dir)
            
            logger.info("="*60)
            logger.info("PROFESSIONAL INSTALLER BUILD COMPLETED!")
            logger.info("="*60)
            logger.info(f"Installer: {final_installer}")
            logger.info(f"Size: {final_installer.stat().st_size / (1024*1024):.1f} MB")
            logger.info("="*60)
            
            return final_installer
            
        except Exception as e:
            logger.error(f"Build failed: {e}")
            raise
        finally:
            self.cleanup()


def main():
    """Main entry point"""
    try:
        builder = SimpleInstallerBuilder()
        installer_path = builder.build()
        
        print("\n" + "="*60)
        print("🎉 PROFESSIONAL INSTALLER CREATED! 🎉")
        print("="*60)
        print(f"📦 Installer: {installer_path.name}")
        print(f"📁 Location: {INSTALLER_OUTPUT_DIR}")
        print(f"🏢 Company: {COMPANY_NAME}")
        print(f"📱 Application: {APP_NAME}")
        print(f"🔢 Version: {APP_VERSION}")
        print("="*60)
        print("✅ Extract and run 'install.bat' as Administrator")
        print("✅ Professional installation with shortcuts")
        print("✅ Ready for distribution!")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ BUILD FAILED: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
